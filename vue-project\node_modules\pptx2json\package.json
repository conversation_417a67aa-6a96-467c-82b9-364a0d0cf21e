{"name": "pptx2json", "version": "0.0.10", "description": "Parse a Powerpoint file to a Json.", "keywords": ["powerpoint", "pptx", "openxml", "json"], "homepage": "https://github.com/x1-/pptx2json", "author": "x1- <<EMAIL>> (https://x1.inkenkun.com)", "license": "MIT", "main": "index.js", "scripts": {"test": "jest --detect<PERSON><PERSON><PERSON><PERSON><PERSON> --forceExit"}, "dependencies": {"jszip": "^3.7.0", "xml2js": "^0.5.0"}, "devDependencies": {"jest": "^27.0.6"}, "jest": {"testPathIgnorePatterns": ["/node_modules/", "/config"], "testTimeout": 30000}}