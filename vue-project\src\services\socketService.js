/**
 * Socket.io连接服务
 * 封装WebSocket连接和事件处理
 */
import { io } from 'socket.io-client';
import { ref, reactive } from 'vue';
import { ElMessage, ElNotification } from 'element-plus';

// Socket连接状态
export const socketStatus = reactive({
  connected: false,
  userId: '',
  userName: '',
  role: '',
  users: []
});

// 控制权信息
export const controlStatus = reactive({
  isControlGranted: false,
  controllingUserId: null,
  controllingUserName: null,
  hasFullAccess: false
});

// 课堂信息
export const classroomStatus = reactive({
  isTeacher: false,
  inClassroom: false,
  classCode: '',
  teacherName: '',
  students: []
});

// 连接配置 - 确保使用正确的端口3002
const SOCKET_URL = 'http://localhost:3002';
let socket = null;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;
const RECONNECT_DELAY = 3000; // 3秒

// 课堂事件回调
const classroomEventCallbacks = {
  onClassroomCreated: null,
  onJoinClassroomSuccess: null,
  onStudentJoined: null,
  onClassroomStudentsUpdated: null
};

/**
 * 注册课堂事件回调
 * @param {string} event - 事件名称
 * @param {Function} callback - 回调函数
 */
export function registerClassroomEventCallback(event, callback) {
  if (classroomEventCallbacks.hasOwnProperty(event)) {
    classroomEventCallbacks[event] = callback;
  }
}

/**
 * 初始化Socket连接
 * @param {Object} user - 用户信息 {userId, userName, role}
 * @returns {Object} socket实例
 */
export function initSocket(user) {
  // 如果已经有连接，先断开
  if (socket) {
    console.log('已存在Socket连接，断开重连');
    socket.disconnect();
    socket = null;
  }
  
  console.log('初始化Socket连接...', user);
  reconnectAttempts = 0;
  
  // 创建连接，添加自动重连配置
  socket = io(SOCKET_URL, {
    reconnection: true,
    reconnectionAttempts: MAX_RECONNECT_ATTEMPTS,
    reconnectionDelay: RECONNECT_DELAY,
    timeout: 10000,
    transports: ['websocket', 'polling'] // 优先使用WebSocket
  });
  
  // 连接事件处理
  socket.on('connect', () => {
    console.log('WebSocket连接成功, ID:', socket.id);
    socketStatus.connected = true;
    reconnectAttempts = 0;
    
    // 发送用户登录信息
    socket.emit('user-login', {
      userId: user.userId,
      userName: user.userName,
      role: user.role
    });
    
    // 更新本地状态
    socketStatus.userId = user.userId;
    socketStatus.userName = user.userName;
    socketStatus.role = user.role;
    classroomStatus.isTeacher = user.role === '教师';
    
    // 移除连接成功消息
    // ElMessage.success('实时通信已连接');
  });
  
  // 重连尝试事件
  socket.on('reconnect_attempt', (attempt) => {
    reconnectAttempts = attempt;
    console.log(`尝试重新连接 (${attempt}/${MAX_RECONNECT_ATTEMPTS})...`);
    
    if (attempt === 1) {
      // 移除重连提示
      // ElMessage.warning('正在尝试重新连接服务器...');
    }
  });
  
  // 重连成功事件
  socket.on('reconnect', () => {
    console.log('重新连接成功!');
    ElMessage.success('已重新连接到服务器');
    
    // 重新发送用户信息
    if (socketStatus.userId) {
      socket.emit('user-login', {
        userId: socketStatus.userId,
        userName: socketStatus.userName,
        role: socketStatus.role
      });
    }
  });
  
  // 重连错误
  socket.on('reconnect_error', (error) => {
    console.error('重连错误:', error);
  });
  
  // 重连失败
  socket.on('reconnect_failed', () => {
    console.error('重连失败，已达到最大尝试次数');
    ElMessage.error('无法连接到服务器，请刷新页面重试');
  });
  
  // 控制被拒绝事件 - 已禁用警告提示
  socket.on('control-denied', (data) => {
    // 静默处理控制权拒绝，不显示任何警告
    // console.warn('控制权被拒绝:', data);
    // ElMessage.warning(data.message || '您没有控制权限');
  });
  
  socket.on('control-error', (data) => {
    console.error('控制权错误:', data);
    ElMessage.error(data.message || '控制权操作失败');
  });
  
  // 获得控制权通知
  socket.on('control-granted-to-you', (data) => {
    console.log('获得控制权:', data);
    controlStatus.isControlGranted = true;
    controlStatus.controllingUserId = socketStatus.userId;
    controlStatus.controllingUserName = socketStatus.userName;
    controlStatus.hasFullAccess = data.hasFullAccess || false;
    
    ElMessage.success(data.message || '您已获得控制权');
    
    // 显示功能提示
    ElNotification({
      title: '控制权已获得',
      message: '您现在可以：\n1. 切换PPT页面\n2. 使用绘图工具\n3. 执行所有教师端操作',
      type: 'success',
      duration: 5000,
      position: 'bottom-right'
    });
  });
  
  // 投票相关事件处理
  socket.on('vote-submitted', (data) => {
    console.log('投票提交确认:', data);
    if (data.success) {
      ElMessage.success(data.message || '投票提交成功');
    }
  });
  
  socket.on('vote-error', (data) => {
    console.error('投票错误:', data);
    ElMessage.error(data.message || '投票提交失败');
  });
  
  socket.on('vote-created', (data) => {
    console.log('投票创建确认:', data);
    if (data.success) {
      ElMessage.success(data.message || '投票创建成功');
    }
  });
  
  socket.on('vote-end-confirmed', (data) => {
    console.log('投票结束确认:', data);
    if (data.success) {
      ElMessage.success(data.message || '投票已结束');
    }
  });

  // 抢答相关事件处理
  socket.on('quick-response-started', (data) => {
    console.log('收到抢答开始通知:', data);
    if (socketStatus.role === '学生') {
      ElNotification({
        title: '抢答开始',
        message: `${data.title} - 快来抢答吧！`,
        type: 'warning',
        duration: 5000,
        position: 'top-right'
      });
    }
  });

  socket.on('quick-response-submitted', (data) => {
    console.log('抢答提交确认:', data);
    if (data.success) {
      ElMessage.success(data.message || '抢答提交成功');
    }
  });

  socket.on('quick-response-ended', (data) => {
    console.log('抢答结束通知:', data);
    ElMessage.info(data.message || '抢答已结束');
  });
  
  // 断开连接事件
  socket.on('disconnect', () => {
    console.log('WebSocket连接断开');
    socketStatus.connected = false;
    // 移除断开连接消息
    // ElMessage.warning('实时通信已断开');
  });
  
  // 连接错误处理
  socket.on('connect_error', (error) => {
    console.error('连接错误:', error);
    socketStatus.connected = false;
    // 移除错误消息通知
    // ElMessage.error('通信服务连接失败');
  });
  
  // 用户列表更新
  socket.on('users-updated', (users) => {
    socketStatus.users = users;
  });
  
  // 课堂相关事件
  socket.on('classroom-created', (data) => {
    console.log('收到classroom-created事件:', data);
    
    // 立即更新课堂状态
    classroomStatus.inClassroom = true;
    classroomStatus.classCode = data.classCode;
    classroomStatus.teacherName = socketStatus.userName;
    
    // 显示成功消息和课堂码
    ElMessage.success(`成功创建课堂，课堂码: ${data.classCode}`);
    
    // 显示课堂码弹窗，确保用户能看到
    ElNotification({
      title: '课堂创建成功',
      message: `课堂码: ${data.classCode}，请分享给学生`,
      type: 'success',
      duration: 0, // 不自动关闭
      position: 'top-right'
    });
    
    // 添加调试信息
    console.log('课堂状态已更新:', classroomStatus);
    
    // 使用alert确保用户能看到课堂码（在某些情况下可能需要）
    setTimeout(() => {
      if (classroomStatus.inClassroom && classroomStatus.classCode) {
        // 使用原生alert确保用户看到课堂码
        alert(`课堂创建成功！\n\n课堂码: ${classroomStatus.classCode}\n\n请记住此课堂码并分享给学生`);
      }
    }, 500);
    
    // 如果有注册回调，则调用
    if (classroomEventCallbacks.onClassroomCreated) {
      classroomEventCallbacks.onClassroomCreated(data);
    }
  });
  
  // 答题相关事件
  socket.on('student-answer-received', (data) => {
    console.log('收到学生答题:', data);
    if (socketStatus.role === '教师') {
      ElNotification({
        title: '学生答题',
        message: `${data.userName || '学生'} 完成了一道题目`,
        type: 'info',
        duration: 3000
      });
    }
  });
  
  socket.on('student-exam-completed', (data) => {
    console.log('学生完成考试:', data);
    if (socketStatus.role === '教师') {
      ElNotification({
        title: '考试完成',
        message: `${data.userName || '学生'} 完成了考试，得分: ${data.score || '未知'}`,
        type: 'success',
        duration: 5000
      });
    }
  });
  
  socket.on('join-classroom-success', (data) => {
    classroomStatus.inClassroom = true;
    classroomStatus.classCode = data.classCode;
    classroomStatus.teacherName = data.teacherName;
    ElMessage.success(data.message);
  });
  
  socket.on('join-classroom-error', (data) => {
    ElMessage.error(data.message);
  });
  
  socket.on('student-joined', (data) => {
    ElMessage.info(`学生 ${data.userName} 加入了课堂`);
  });
  
  socket.on('classroom-students-updated', (data) => {
    classroomStatus.students = data.students;
  });
  
  // 控制权变更事件
  socket.on('control-granted', (data) => {
    controlStatus.isControlGranted = true;
    controlStatus.controllingUserId = data.userId;
    controlStatus.controllingUserName = data.userName;
    controlStatus.hasFullAccess = data.hasFullAccess || false;
    
    // 如果自己获得控制权，显示提示
    if (data.userId === socketStatus.userId) {
      ElMessage.success(`您已获得控制权`);
    } else {
      ElMessage.info(`${data.userName} 已获得控制权`);
    }
  });
  
  socket.on('control-revoked', () => {
    controlStatus.isControlGranted = false;
    controlStatus.controllingUserId = null;
    controlStatus.controllingUserName = null;
    controlStatus.hasFullAccess = false;
    ElMessage.info('控制权已被收回');
  });
  
  return socket;
}

/**
 * 检查WebSocket连接状态
 * @returns {boolean} 是否已连接
 */
export function checkSocketConnection() {
  return socket !== null && socket.connected;
}

/**
 * 获取socket实例
 * @returns {Object|null} socket实例
 */
export function getSocket() {
  // 不再显示弹窗警告，只在控制台打印消息
  if (!socket) {
    console.warn('WebSocket未连接，某些功能可能无法正常使用');
    return null;
  }
  return socket;
}

/**
 * 教师创建课堂
 * @returns {Promise} 创建课堂的Promise
 */
export function createClassroom() {
  if (!socket) {
    console.error('Socket未初始化，无法创建课堂');
    ElMessage.error('通信服务未连接，请刷新页面重试');
    return Promise.reject(new Error('Socket未初始化'));
  }
  
  if (!socket.connected) {
    console.error('Socket未连接，正在尝试重连');
    ElMessage.warning('通信服务连接中，请稍后再试');
    socket.connect(); // 尝试重新连接
    return Promise.reject(new Error('Socket未连接'));
  }
  
  return new Promise((resolve, reject) => {
    const userData = {
      userId: socketStatus.userId || localStorage.getItem('userId'),
      userName: socketStatus.userName || localStorage.getItem('userName')
    };
    
    console.log('发送create-classroom事件', userData);
    
    // 检查socket连接状态
    console.log('Socket连接状态:', socket.connected ? '已连接' : '未连接');
    
    // 添加一次性事件监听器，等待创建课堂的响应
    socket.once('classroom-created', (data) => {
      console.log('收到classroom-created响应:', data);
      resolve(data);
    });
    
    // 设置超时
    const timeoutId = setTimeout(() => {
      console.error('创建课堂超时');
      reject(new Error('创建课堂超时，服务器未响应'));
    }, 5000);
    
    // 发送创建课堂事件
    socket.emit('create-classroom', userData, (response) => {
      clearTimeout(timeoutId);
      
      if (response && response.error) {
        console.error('创建课堂失败:', response.error);
        reject(new Error(response.error));
      }
    });
    
    // 添加调试日志
    console.log('create-classroom事件已发送，等待服务器响应');
  });
}

/**
 * 学生加入课堂
 * @param {string} classCode - 课堂码
 */
export function joinClassroom(classCode) {
  if (!socket) {
    console.error('Socket未初始化，无法加入课堂');
    ElMessage.error('通信服务未连接，请刷新页面重试');
    return;
  }
  
  // 确保使用localStorage中存储的用户名，它是用户注册或登录时的名字
  const userName = localStorage.getItem('userName') || socketStatus.userName;
  
  console.log(`尝试加入课堂，课堂码: ${classCode}, 用户: ${userName}(${socketStatus.userId})`);
  
  socket.emit('join-classroom', {
    userId: socketStatus.userId,
    userName: userName, // 使用本地存储的用户名，确保是学生注册的名字
    classCode
  });
}

/**
 * 离开当前课堂
 */
export function leaveClassroom() {
  if (!socket || !classroomStatus.inClassroom) return;
  
  socket.emit('leave-classroom', {
    userId: socketStatus.userId,
    classCode: classroomStatus.classCode
  });
  
  // 更新本地状态
  classroomStatus.inClassroom = false;
  classroomStatus.classCode = '';
  classroomStatus.teacherName = '';
  
  ElMessage.info('已离开课堂');
}

/**
 * 授予学生控制权
 * @param {string} userId - 学生ID
 */
export function grantControl(userId) {
  if (!socket) return;
  
  socket.emit('grant-control', userId);
}

/**
 * 收回控制权
 */
export function revokeControl() {
  if (!socket) return;
  
  socket.emit('revoke-control');
}

/**
 * 发送PPT页面切换事件
 * @param {Object} slideData - 页面数据 {slideIndex, totalSlides}
 */
export function sendSlideChange(slideData) {
  if (!socket) return;
  
  socket.emit('slide-change', slideData);
}

/**
 * 发送学生提问
 * @param {Object} questionData - 问题数据
 */
export function sendQuestion(questionData) {
  if (!socket) return;
  
  socket.emit('ask-question', questionData);
}

/**
 * 创建投票
 * @param {Object} voteData - 投票数据
 */
export function createVote(voteData) {
  if (!socket) return;

  socket.emit('create-vote', voteData);
}

/**
 * 发起抢答
 * @param {Object} responseData - 抢答数据
 */
export function startQuickResponse(responseData) {
  if (!socket) return;

  socket.emit('start-quick-response', responseData);
}

/**
 * 提交抢答
 * @param {Object} answerData - 抢答答案数据
 */
export function submitQuickResponse(answerData) {
  if (!socket) return;

  socket.emit('submit-quick-response', answerData);
}

/**
 * 提交投票选择
 * @param {Object} voteResult - 投票结果
 */
export function submitVote(voteResult) {
  if (!socket) return;
  
  socket.emit('submit-vote', voteResult);
}

/**
 * 结束投票
 * @param {string} voteId - 投票ID
 */
export function endVote(voteId) {
  if (!socket) return;
  
  socket.emit('end-vote', voteId);
}

/**
 * 学生提交答题结果
 * @param {Object} answerData - 答题数据
 */
export function sendStudentAnswer(answerData) {
  if (!socket) {
    console.error('Socket未初始化，无法发送答题结果');
    return false;
  }
  
  console.log('发送student-answer事件', answerData);
  socket.emit('student-answer', answerData);
  return true;
}

/**
 * 学生完成考试
 * @param {Object} examData - 考试结果数据
 */
export function sendExamCompletion(examData) {
  if (!socket) {
    console.error('Socket未初始化，无法发送考试完成通知');
    return false;
  }
  
  console.log('发送student-exam-completed事件', examData);
  socket.emit('student-exam-completed', examData);
  return true;
}

/**
 * 断开Socket连接
 */
export function disconnectSocket() {
  if (socket) {
    socket.disconnect();
    socket = null;
    socketStatus.connected = false;
    console.log('WebSocket连接已断开');
  }
} 