{"name": "vue-project", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "start": "node start.js", "server": "node server.js"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "axios": "^1.9.0", "body-parser": "^2.2.0", "cors": "^2.8.5", "element-plus": "^2.3.8", "express": "^4.18.2", "idb": "^7.1.1", "jszip": "^3.10.1", "mysql2": "^3.14.1", "pinia": "^2.0.32", "pptx2json": "^0.0.10", "socket.io": "^4.7.2", "socket.io-client": "^4.7.2", "vue": "^3.3.4", "vue-router": "^4.2.2", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}