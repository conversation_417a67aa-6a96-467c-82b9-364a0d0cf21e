<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { getResources, deleteResource } from '../services/db/resourceService'
import PageHeader from '../components/PageHeader.vue'
import ResourceUploader from '../components/ResourceUploader.vue'

const router = useRouter()

// 模拟资源数据
const resourceList = ref([])
const loading = ref(false)

// 资源类型
const resourceTypes = [
  { label: '全部', value: 'all' },
  { label: 'PPT课件', value: 'ppt' },
  { label: '教学视频', value: 'video' },
  { label: '教案', value: 'document' },
  { label: '习题', value: 'exercise' }
]

// 搜索条件
const searchForm = reactive({
  keyword: '',
  type: 'all',
  subject: 'all'
})

// 模拟学科数据
const subjectOptions = [
  { label: '全部学科', value: 'all' },
  { label: '语文', value: 'chinese' },
  { label: '数学', value: 'math' },
  { label: '英语', value: 'english' },
  { label: '物理', value: 'physics' },
  { label: '化学', value: 'chemistry' },
  { label: '生物', value: 'biology' },
  { label: '历史', value: 'history' },
  { label: '地理', value: 'geography' },
  { label: '政治', value: 'politics' }
]

// 获取资源列表
const getResourceList = async () => {
  loading.value = true
  
  try {
    // 准备过滤条件
    const filters = {
      keyword: searchForm.keyword,
      type: searchForm.type === 'all' ? '' : searchForm.type,
      subject: searchForm.subject === 'all' ? '' : searchForm.subject
    }
    
    // 从数据库获取资源
    const resources = await getResources(filters)
    resourceList.value = resources
  } catch (error) {
    console.error('获取资源列表失败:', error)
    ElMessage.error('获取资源列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索资源
const handleSearch = () => {
  getResourceList()
}

// 重置搜索
const resetSearch = () => {
  searchForm.keyword = ''
  searchForm.type = 'all'
  searchForm.subject = 'all'
  handleSearch()
}

// 上传资源
const uploadDialogVisible = ref(false)

// 打开上传对话框
const openUploadDialog = () => {
  uploadDialogVisible.value = true
}

// 资源上传成功后的回调
const handleResourceUploaded = () => {
  getResourceList() // 刷新资源列表
}

// 加载数据
onMounted(() => {
  getResourceList()
})

// 预览资源
const previewResource = (resource) => {
  if (resource.type === 'ppt' && resource.isPPT) {
    // 如果是PPT，打开PPT预览
    openPPTPreview(resource);
  } else {
    ElMessage.info(`预览资源: ${resource.title}`);
  }
}

// PPT预览对话框状态
const pptPreviewVisible = ref(false)
const currentPPTResource = ref(null)

// 打开PPT预览
const openPPTPreview = (resource) => {
  currentPPTResource.value = resource
  pptPreviewVisible.value = true
}

// 下载PPT文件
const downloadPPTFile = (resource) => {
  try {
    // 如果文件URL是Base64格式，创建下载链接
    if (resource.fileUrl && resource.fileUrl.startsWith('data:')) {
      const link = document.createElement('a')
      link.href = resource.fileUrl
      link.download = resource.fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      ElMessage.success('开始下载PPT文件')
    } else {
      ElMessage.warning('文件不可用')
    }
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('下载失败')
  }
}

// 下载资源
const downloadResource = (resource) => {
  ElMessage.success(`开始下载: ${resource.title}`)
}

// 查看资源详情
const viewResourceDetail = (resource) => {
  router.push(`/dashboard/resource/${resource.id}`)
}

// 删除资源
const deleteResourceItem = async (resource) => {
  ElMessageBox.confirm(
    `确定要删除资源 "${resource.title}" 吗？`, 
    '删除确认', 
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      // 从数据库删除资源
      await deleteResource(resource.id)
      // 从列表中移除资源
      resourceList.value = resourceList.value.filter(item => item.id !== resource.id)
      ElMessage.success('资源删除成功')
    } catch (error) {
      console.error('删除资源失败:', error)
      ElMessage.error('删除资源失败')
    }
  }).catch(() => {
    // 取消删除
  })
}
</script>

<template>
  <div class="resources-container">
    <!-- 页面标题 -->
    <PageHeader 
      title="优质课件资源库" 
      subtitle="发现、上传和分享优质教学资源"
    >
      <template #actions>
        <el-button type="primary" @click="openUploadDialog">
          <el-icon><Upload /></el-icon>
          上传资源
        </el-button>
      </template>
    </PageHeader>
    
    <!-- 搜索区域 -->
    <div class="search-area card-panel">
      <el-form :model="searchForm" inline>
        <el-form-item>
          <el-input 
            v-model="searchForm.keyword" 
            placeholder="请输入关键词搜索" 
            clearable
            prefix-icon="Search"
          />
        </el-form-item>
        
        <el-form-item>
          <el-select v-model="searchForm.type" placeholder="资源类型">
            <el-option 
              v-for="type in resourceTypes" 
              :key="type.value" 
              :label="type.label" 
              :value="type.value" 
            />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-select v-model="searchForm.subject" placeholder="学科">
            <el-option 
              v-for="subject in subjectOptions" 
              :key="subject.value" 
              :label="subject.label" 
              :value="subject.value" 
            />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 资源列表 -->
    <div class="resource-list">
      <el-empty v-if="!loading && resourceList.length === 0" description="暂无资源，请尝试其他搜索条件或上传新资源" />
      
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="resource in resourceList" :key="resource.id">
          <el-card class="resource-card" shadow="hover">
            <div class="resource-cover" @click="viewResourceDetail(resource)">
              <img :src="resource.coverUrl" :alt="resource.title">
              <div class="resource-type">
                {{ resourceTypes.find(t => t.value === resource.type)?.label || '通用' }}
              </div>
            </div>
            <div class="resource-info">
              <h3 class="resource-title" :title="resource.title" @click="viewResourceDetail(resource)">
                {{ resource.title }}
              </h3>
              <p class="resource-desc" :title="resource.description">{{ resource.description }}</p>
              <div class="resource-meta">
                <span>
                  <el-icon><Calendar /></el-icon>
                  {{ resource.uploadTime }}
                </span>
                <span>
                  <el-icon><Download /></el-icon>
                  {{ resource.downloads }}
                </span>
                <span>
                  <el-icon><Document /></el-icon>
                  {{ resource.fileSize }}
                </span>
              </div>
              <div class="resource-actions">
                <el-button-group>
                  <el-button size="small" @click="viewResourceDetail(resource)">
                  <el-icon><View /></el-icon>
                    详情
                </el-button>
                <el-button type="primary" size="small" @click="downloadResource(resource)">
                  <el-icon><Download /></el-icon>
                  下载
                  </el-button>
                </el-button-group>
                <el-button type="danger" size="small" @click="deleteResourceItem(resource)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 加载中 -->
    <div v-if="loading" class="loading-container">
      <div class="skeleton-container">
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="i in 8" :key="i">
            <div class="skeleton-card">
              <el-skeleton animated>
                <template #template>
                  <div style="padding: 14px">
                    <el-skeleton-item variant="image" style="width: 100%; height: 160px" />
                    <div style="padding: 14px">
                      <el-skeleton-item variant="h3" style="width: 80%" />
                      <div style="display: flex; align-items: center; margin: 16px 0">
                        <el-skeleton-item variant="text" style="margin-right: 16px" />
                        <el-skeleton-item variant="text" style="width: 30%" />
                      </div>
                      <el-skeleton-item variant="text" style="width: 100%" />
                    </div>
                  </div>
                </template>
              </el-skeleton>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    
    <!-- 上传对话框 -->
    <div>
      <ResourceUploader
        v-model:visible="uploadDialogVisible"
        @uploaded="handleResourceUploaded"
      />
    </div>

    <!-- PPT预览对话框 -->
    <el-dialog
      v-model="pptPreviewVisible"
      :title="`PPT预览 - ${currentPPTResource?.title || ''}`"
      width="80%"
      top="5vh"
      :before-close="() => { pptPreviewVisible = false; currentPPTResource = null }"
    >
      <div v-if="currentPPTResource" class="ppt-preview-container">
        <div class="ppt-info">
          <h3>{{ currentPPTResource.title }}</h3>
          <p><strong>文件名：</strong>{{ currentPPTResource.fileName }}</p>
          <p><strong>文件大小：</strong>{{ currentPPTResource.fileSize }}</p>
          <p><strong>上传时间：</strong>{{ currentPPTResource.uploadTime }}</p>
          <p><strong>描述：</strong>{{ currentPPTResource.description }}</p>
        </div>

        <div class="ppt-actions">
          <el-button type="primary" @click="downloadPPTFile(currentPPTResource)">
            <el-icon><Download /></el-icon>
            下载PPT文件
          </el-button>
          <el-button @click="() => { pptPreviewVisible = false; currentPPTResource = null }">
            关闭预览
          </el-button>
        </div>

        <div class="ppt-preview-note">
          <el-alert
            title="PPT预览说明"
            description="由于浏览器限制，无法直接在网页中预览PPT内容。请下载文件后使用PowerPoint或其他兼容软件打开查看。"
            type="info"
            :closable="false"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped>
.resources-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-area {
  margin-bottom: 24px;
}

.resource-list {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 20px;
}

.resource-card {
  margin-bottom: 20px;
  transition: all 0.3s;
  border: none;
  overflow: hidden;
  border-radius: 8px;
}

.resource-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.resource-cover {
  position: relative;
  height: 180px;
  overflow: hidden;
  border-radius: 8px 8px 0 0;
}

.resource-cover::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(to top, rgba(0,0,0,0.6), transparent);
}

.resource-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s;
}

.resource-card:hover .resource-cover img {
  transform: scale(1.08);
}

.resource-type {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 4px 10px;
  background-color: rgba(24, 144, 255, 0.9);
  color: white;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  z-index: 1;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.resource-info {
  padding: 18px;
}

.resource-title {
  font-size: 16px;
  margin: 0 0 10px;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--text-primary);
}

.resource-desc {
  font-size: 14px;
  color: var(--text-regular);
  margin: 0 0 16px;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.5;
}

.resource-meta {
  display: flex;
  font-size: 13px;
  color: var(--text-secondary);
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.resource-meta span {
  display: flex;
  align-items: center;
  margin-right: 12px;
  margin-bottom: 4px;
}

.resource-meta .el-icon {
  margin-right: 4px;
  font-size: 14px;
}

.resource-actions {
  display: flex;
  justify-content: space-between;
}

.resource-actions .el-button {
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.3s;
}

.resource-actions .el-button:hover {
  transform: translateY(-2px);
}

.loading-container {
  padding: 20px;
}

.skeleton-card {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
  background-color: white;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

@media (max-width: 768px) {
  .el-form-item {
    margin-bottom: 10px;
  }
  
  .search-area .el-form {
    display: flex;
    flex-direction: column;
  }
  
  .search-area .el-form-item {
    width: 100%;
    margin-right: 0;
  }
  
  .search-area .el-input,
  .search-area .el-select {
    width: 100%;
  }
  
  .resource-cover {
    height: 160px;
  }
  
  .resource-title {
    font-size: 15px;
  }
}

.ppt-preview-container {
  padding: 20px;
}

.ppt-info {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.ppt-info h3 {
  margin: 0 0 15px 0;
  color: #303133;
}

.ppt-info p {
  margin: 8px 0;
  color: #606266;
}

.ppt-actions {
  text-align: center;
  margin-bottom: 20px;
}

.ppt-actions .el-button {
  margin: 0 10px;
}

.ppt-preview-note {
  margin-top: 20px;
}
</style>